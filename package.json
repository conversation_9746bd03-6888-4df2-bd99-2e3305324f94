{"private": true, "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-yaml": "^6.1.2", "@tailwindcss/typography": "^0.5.16", "@uiw/react-codemirror": "^4.23.10", "@uiw/react-json-view": "^2.0.0-alpha.33", "@umijs/plugins": "4.4.11", "antd": "^5.24.7", "axios": "^1.6.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dva": "^2.4.1", "echarts": "^6.0.0", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "mermaid": "^11.10.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "path-to-regexp": "^8.2.0", "qiankun": "^2.10.16", "react-cookies": "^0.1.1", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "8.0.7", "react-redux": "^9.1.0", "react-svg": "^16.1.34", "react-syntax-highlighter": "^15.6.6", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "rehype-katex": "^7.0.1", "remark-gfm": "3.0.1", "remark-math": "^6.0.0", "umi": "4.4.11"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "autoprefixer": "^10.4.21", "@umijs/plugin-qiankun": "^2.44.1", "babel-plugin-transform-remove-console": "^6.9.4", "postcss": "^8.5.6", "tailwindcss": "3.4.17", "typescript": "^5.0.3"}}