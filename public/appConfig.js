// 天合开发环境
window._th_appInfo = {
  _appId: "1",
  _middleware: {
    // 指定组织、项目、集群、命名空间
    defaultEnv: "prod",
    envMap: {
      // 环境配置 dev，test，uat，prod
      prod: {
        project: {
          organId: "17da50d2422242b4",
          projectId: "15321cf3f00a4e5c",
          organName: "数字化产品研发部",
          projectName: "应用服务平台",
        },
        excludeStorageIds:["73140547e3fc401e"],
      },
      test: {
        project: {
          organId: "17da50d2422242b4",
          projectId: "15321cf3f00a4e5c",
          organName: "数字化产品研发部",
          projectName: "应用服务平台",
        },
        excludeStorageIds:["335c791681c74a53"],
        // 指定默认集群和命名空间
        clusterId: "default--test",
        namespace: "tasp-test",
        // 设置默认集群后，不可更换
        clusterSeletable: false,
      },
    },
  },
  _middlewareInfo: {
    defaultEnv: "dev",
    envMap: {
      // 环境配置 dev，test，uat，prod
      prod: {
        middlewareIpPort: "",
      },
      dev: {
        middlewareIpPort: "http://***********:31088/#",
      },
      test: {
        middlewareIpPort: "",
      },
    },
  },
  // 智能助手相关配置
  _RAG: {
    // 智能助手的js资源地址，ip端口由nginx转发
    url: "https://tasp-dev.trinasolar.com/rag-api/applications/embed/js?protocol=https&host=tasp-dev.trinasolar.com&token=UBJQssCEjS7VgHc0dRFDQaW7xCE6dsN-Yk-rpXKJsfA",
    // 哪些应用启动智能助手，配置微应用的name
    apps:['dochub'],
    // 智能搜索rag应用的id
    appId: "82c9a74f-6263-4fed-b8cd-ddc7d67fd638",
    // 智能搜索rag应用的token
    token: "kxDyZzX2IDbEMDxhycTiqLeh7e813p_BaogwLocubkY"
  },
};