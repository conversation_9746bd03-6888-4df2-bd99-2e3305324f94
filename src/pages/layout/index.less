#root-master {
    font-family: @font-family-base;
    font-size: @font-size-normal;
    width: 100vw;
    height: 100vh;
    .layout{
        height: 100%;
        display: flex;
        flex-direction: column;
        & >.main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        & > .home-main {
            flex-direction: column;
        }
        &.filterBlur {
            filter: blur(10px);
            overflow: hidden;
        }
		.sidebar {
			height: 100%;
		}
		.sub-app-box {
			flex: 1;
			height: 100%;
            position: relative;
            background: #f5f5f5;
            overflow-y: auto;
			&.loginBox {
				width: 100%;
				height: 100%;
				padding: 0;
				overflow: inherit;
			}
            .qiankun-micro-app-wrapper {
                height: 100%;
                & > .qiankun-micro-app-container {
                    height: 100%;
                    #root {
                        height: 100%;
                    }
                    & > div {
                        height: 100%;
                    }
                }
                .micro-spin {
                    height: auto;
                }
            }
			&.noPaddingBox {
				padding: 0;
			}
			&.sidebar-temp-expend, &.sidebar-contract {
                margin-left: 62px;
			}
			.ant-page-header {
				padding: 16px 0px;
			}
		}
        .sub-app-box-container {
            width: 100%;
            height: 100%;
            max-height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .sub-app-box-container-content {
                flex: 1;
                margin: 8px 8px;
                background: #fff;
                overflow-y: auto;
            }
        }
    }
	.global-menu {
        width: 100%;
		height: 100%;
		position: absolute;
		background: rgba(255, 255, 255, 0.6);
		top:0;
		z-index: 9999;
        .menu {  
            width: 100%;
            height: 100%; 
            display: flex;
            align-items: center;
            justify-content: center;
            .menu-item {
                width: 200px;
                height: 100%;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                overflow: hidden;
                .menu-name {     
                    text-align: center;
                    .title-icon {
                        font-size: 35px;
                    }
                    p.name {
                        font-size: 18px;
                        font-weight: 700;
                        line-height: 25px;
                        margin-top: 16px;
                    }
                    p.des {
                        font-size: 14px;
                        margin-top: 16px;
                        height: 16px;
                        line-height: 16px;
                    }
                }
                .background-icon {
                    font-size: 150px;
                    position: absolute;
                    right: -40px;
                    bottom: -55px;
                    color: #fff;
                    opacity: 0;
                    &::after {
                        content: "";
                        position: absolute;
                        right: 0px;
                        bottom: 0px;
                        width: 300%;
                        height: 100%;
                        background-image: linear-gradient(to bottom, rgba(54, 111, 223, 0) 0%, rgba(54, 111, 223, 1) 100%);
                    }
                   
                }
                .sub-menu-item {
                    display: none;   
                    p {
                        width: 120px;
                        font-size: 14px;
                        line-height: 20px;
                        font-weight: 400;
                        padding: 8px 0;
                        color: #fff;
                        text-align: center;
                        position: relative;
                        &:hover {
                            font-weight: 600;
                            transition: all 0.5s;
                            &::after {
                                content: "";
                                position: absolute;
                                width: 120px;
                                height: 2px;
                                background-color: #fff;
                                bottom: 0px;
                                left: 0px;
                                background: radial-gradient(circle, #fff, transparent);
                            }
                        }
                    }
                    
                }
                &:hover {
                    background: #366FDF;
                    color: #fff;
                    transition: all 0.5s;
                    .menu-name {
                        p.name {
                            text-shadow: 0px 2px 6px rgba(1, 31, 90, 0.5);
                        }
                        .title-icon {
                            color: #fff;
                        }
                    }
                    .menu-name.hasChild {
                        display: none;
                    }
                    .sub-menu-item {
                        display: block;
                    }
                    .background-icon {
                        opacity: 0.5;
                    }
                }
            }
        }
        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 9999;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 4px;
            &:hover {
                background-color: #4C92F5;
                color: #fff;
            }
            
        }
		
	}
    .active-link, .hover-link:hover{
        color: @primary-color !important
    }
}

/*** antdesign 标签颜色调整 */
.ant-tag {
	&.ant-tag-blue {
		color: #3C89D0;
		background: #F0F5FF;
		border-color: #ADC6FF;
	}
	&.ant-tag-gold {
		color: #FA8C16;
		background: #FFF7E6;
		border-color: #FFD591;
	}
}
