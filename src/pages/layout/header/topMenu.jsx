import { useModel, history, useLocation, generatePath } from "umi";
import { useEffect, useMemo } from "react";
import { Menu } from "antd";
import { menuLayoutEnum } from "@/enums/menuEnum"
import { topMenusOfDynamicSideBarMenus, appSystemAndProgramTopMenus } from "@/constants"
import { findTopMenuKey, formatMenus, flattenMenuData, getMatchedKeys, jump, findDefaultMenuItem, generateSidebarMenuByParams, findRouteParamsByPath, filterHiddenTopMenus, getSearchParam } from "@/utils/menus"
import storage from "@/utils/storage";
import { sessionStorageEnum } from '@/enums/storageEnum';
import "./index.less";

// 头部顶层菜单
export default function TopMenu({isHome}) {

  const { topMenus, activeTopMenu, setActiveTopMenu, setSidebarMenus, hiddenSideBarMenus, topMenuPathMap, setSelectedKeys, setOpenKeys, currentPath } = useModel("menu");
  const { layoutConfig, changeSideBarVisible } = useModel("sysConfig");
  const { selectAppInfo, setSelectAppInfo, setShowChangeAppInfo, setIsLinkChange } = useModel('appSystemInfo')

  // 格式化菜单数据
  const formatTopMenus = useMemo(
    () => filterHiddenTopMenus(formatMenus(topMenus, true, true)),
    [topMenus]
  );

  // 清空侧栏菜单状态
  const clearSideBarStatus = () => {
    setSidebarMenus([])
    setSelectedKeys([])
    setOpenKeys([])
  }

  // 当侧栏菜单有数据时，点击顶部菜单默认选中侧栏菜单第一项
  const handleClickTopMenuEffect = (menuItem) => {
    const { pathMap, nodeMap } = flattenMenuData(menuItem?.children ?? [])
    const defaultMenuItem = findDefaultMenuItem(menuItem?.children)
    const { selectedKeys: matchedKeys, openKeys: matchedOpenKeys } = getMatchedKeys(
      defaultMenuItem?.path,
      pathMap,
      nodeMap
    );
    setSelectedKeys(matchedKeys)
    setOpenKeys(matchedOpenKeys)
    jump(defaultMenuItem)
  }
  // 点击顶部菜单
  const handleClickHeaderMenu = ({key}) => {
    const menuItem = topMenus.find(item => item.id === key)
    if(!menuItem) return
    clearSideBarStatus()
    if(layoutConfig.menuLayout === menuLayoutEnum.mix){// 混合菜单布局
      setActiveTopMenu({...menuItem});
      setSidebarMenus(menuItem?.children ?? [])

      if( !menuItem?.children?.length  &&  menuItem?.path){// 如果没有二级菜单
        changeSideBarVisible(false)
        jump(menuItem)
      }else{// 有二级菜单
        if(hiddenSideBarMenus.includes(menuItem.path)){// 如果该菜单默认配置了不显示侧边栏菜单
          changeSideBarVisible(false)
          jump(menuItem)
        }else{
          changeSideBarVisible(true)
          handleClickTopMenuEffect(menuItem)
0         }
      }
    }else if(layoutConfig.menuLayout === menuLayoutEnum.header){// 顶部菜单布局
      // TODO:
    }else{// 侧栏布局

      // TODO:
    }
  };

  // 当URL变化时，匹配侧栏菜单选中状态
  const matchSidebarMenuSelectByUrl = (path, pathMap, nodeMap, currentMenuStateCollection) => {
    path = path?.indexOf("master-common-iframe") > -1 || path?.indexOf("illegalPath") > -1 ? getSearchParam('rowPath', path) : path
    const { selectedKeys: matchedKeys, openKeys: matchedOpenKeys } = getMatchedKeys(
      path,
      pathMap,
      nodeMap
    );
    currentMenuStateCollection.selectedKeys = matchedKeys
    currentMenuStateCollection.openKeys = matchedOpenKeys
    setSelectedKeys(matchedKeys);
    setOpenKeys(matchedOpenKeys);
  }

  // 初始化所有状态参数
  const initMenuState = (currMenu) => {
    const currentMenuStateCollection = {// 用于缓存菜单状态，内页刷新时保存菜单状态。
      activeTopMenu: currMenu,
      sidebarMenus: null,
      selectedKeys: [],
      openKeys: [],
      sideBarVisible: false,
      routeParamsInfoCache: null,// 路由参数信息缓存，如果当前访问的是动态路劲，需要根据url动态更新侧栏菜单，但是现在访问的是内页，需要缓存路由参数信息
    }
    // 设置应用程序、应用系统信息组件默认不展示
    if(appSystemAndProgramTopMenus.indexOf(currMenu.path) === -1) setShowChangeAppInfo(false)
    return {
      currentMenuStateCollection
    }
  }
  // 整体逻辑是根据当前访问的URL匹配顶部菜单,设置侧栏菜单数据
  // 说明：1、currMenu: 当前匹配的顶部菜单，你访问侧栏菜单url时，会匹配到相应的顶部菜单，然后再组装sidebarMenus
  //      2、currentPath: 当前访问的url路劲
  //      3、topMenuPathMap: 顶部菜单pathMap，所有属于该顶部菜单的path（包含侧栏菜单数据）与顶部菜单的id进行映射
  //      4、内页url和顶部菜单没有维护对应的关系，只有靠currentMenuStateCollection缓存当前菜单状态，才能保证刷新时，菜单状态不丢失
  //      5、动态侧栏菜单数据形如/kepler-webpage/svc/integrationService/applicationIntegration/detail/:id/inner，会重新根据跳转携带的参数组件侧栏菜单的path，看topMenusOfDynamicSideBarMenus处代码
  //      6、应用系统和应用程序侧栏菜单有切换当前菜单参数的行为，得特殊处理，看appSystemAndProgramTopMenus处代码
  //      7、识别带有master-common-iframe的路劲，走iframe逻辑，设置菜单选中也会走iframe逻辑
  //      8、识别带有illegalPath的路劲，走菜单路劲无效时缺省逻辑，设置菜单选中也会取illegalPath携带的query参数rowPath作为匹配菜单的路劲逻辑(菜单非法路劲问题处理)
  //      9、识别带有master-common-iframe的路劲，走菜单iframe逻辑，设置菜单选中也会取master-common-iframe携带的query参数rowPath作为匹配菜单的路劲逻辑(菜单配成访问外部iframe链接问题处理)
  useEffect(() => {
    if(!topMenus.length) return
    if(topMenus.length && topMenuPathMap){
      let currMenu = topMenus?.[0]
      if(currentPath && currentPath !== '/' && currentPath !== '/backend'){// 当前访问path是有效值，则匹配顶部菜单
        const currentTopKey = currentPath.indexOf('master-common-iframe') > -1 || currentPath.indexOf('illegalPath') > -1 ? 
        findTopMenuKey(getSearchParam('rowPath', currentPath), topMenuPathMap) : 
        findTopMenuKey(currentPath, topMenuPathMap);
        currMenu = topMenus?.find(item => item.id === currentTopKey)
      }else{// 当前访问path不是效值，则匹配第一个顶部菜单并跳转
        currMenu.children = currMenu.children ?? []
        if(!currMenu.children?.length && currMenu?.path){
          history.replace(currMenu?.path)
          return
        }else{
          handleClickTopMenuEffect(currMenu)
        }
      }
      if(currMenu){// 匹配上了顶部菜单的情况
        const { pathMap, nodeMap } = flattenMenuData(currMenu?.children ?? [])
        const { currentMenuStateCollection } = initMenuState(currMenu)
        if(window._MiCROAPP_JUMP_INFO_.has(currentPath)){// 子应用跳转信息含有该路径，说明是子应用跳转过来的，需要重新跳转对应真实访问的地址
          const params = window._MiCROAPP_JUMP_INFO_.get(currentPath)
          window._MiCROAPP_JUMP_INFO_.delete(currentPath)
          const jumpPath = generatePath(currentPath, params)
          jumpPath && history.replace(jumpPath)
          return
        }
        // 如果当前访问的是动态路劲，需要根据url动态更新侧栏菜单，如应用系统、应用程序两个顶部菜单的侧栏菜单数据是动态的
        if(topMenusOfDynamicSideBarMenus.includes(currMenu.path) && currMenu.path !== currentPath){
          // 根据当前currentPath 匹配对应的路由参数
          const routeParamsInfo = findRouteParamsByPath(currentPath, pathMap)
          currentMenuStateCollection.routeParamsInfoCache = routeParamsInfo
          if(routeParamsInfo?.params && Object.keys(routeParamsInfo?.params)?.length > 0){// 如果匹配到了路由参数
            if(appSystemAndProgramTopMenus.includes(currMenu.path)){// 如果当前访问的是应用系统、应用程序的菜单，需要特殊处理
              setShowChangeAppInfo(true)
              const appSystemName = routeParamsInfo?.params?.id === selectAppInfo?.appSystemId ? selectAppInfo?.appSystemName : null
              const appProgramName = routeParamsInfo?.params?.systemId === selectAppInfo?.appProgramId ? selectAppInfo?.appProgramName : null
              const selectAppInfoTemp = {
                appSystemId: routeParamsInfo?.params?.id ?? null,
                appSystemName,
                appProgramId: routeParamsInfo?.params?.systemId ??  null,
                appProgramName
              }
              setSelectAppInfo(selectAppInfoTemp)
              setIsLinkChange(true)
            }
            currMenu.children = generateSidebarMenuByParams(currMenu.children, routeParamsInfo.params)
          }
        }
        if(appSystemAndProgramTopMenus.includes(currMenu.path) && currMenu.path !== appSystemAndProgramTopMenus[0]){// 当访问应用程序菜单时设置应用系统顶部菜单激活
          // 找到应用系统顶部菜单并设置激活
          const appSystemTopMenu = topMenus.find(item => item.path === appSystemAndProgramTopMenus[0])
          setActiveTopMenu(appSystemTopMenu)
          currentMenuStateCollection.activeTopMenu = appSystemTopMenu
        }else{// 当访问其他顶部菜单走正常逻辑
          setActiveTopMenu(currMenu)
        }
        const currSidebarMenus = currMenu?.children ?? []
        setSidebarMenus(currSidebarMenus)
        currentMenuStateCollection.sidebarMenus = currSidebarMenus
        if((currMenu.path === currentPath && hiddenSideBarMenus.includes(currMenu.path)) || currSidebarMenus.length === 0){
          // 如果当前访问路劲是顶部菜单的path且该菜单默认配置了不显示侧边栏菜单，或者当前顶部菜单没有子菜单
          changeSideBarVisible(false)
          currentMenuStateCollection.sideBarVisible = false
        } else {
          changeSideBarVisible(true)
          currentMenuStateCollection.sideBarVisible = true
          matchSidebarMenuSelectByUrl(currentPath, pathMap, nodeMap, currentMenuStateCollection)
        }
        storage.setSession(sessionStorageEnum.currentMenuStateCollection, currentMenuStateCollection)
      }else{// 匹配不上顶部菜单的情况，比如一些内页匹配不上菜单数据，菜单状态保持原样
        const currentMenuStateCollection = storage.getSession(sessionStorageEnum.currentMenuStateCollection)
        if(currentMenuStateCollection){
          let { activeTopMenu, sidebarMenus, selectedKeys, openKeys, sideBarVisible, routeParamsInfoCache } = currentMenuStateCollection
          // 如果当前访问的是动态路劲，需要根据url动态更新侧栏菜单，如应用系统、应用程序两个顶部菜单的侧栏菜单数据是动态的
          if(topMenusOfDynamicSideBarMenus.includes(activeTopMenu.path)){
            // 根据当前currentPath 匹配对应的路由参数
            if(routeParamsInfoCache?.params && Object.keys(routeParamsInfoCache?.params)?.length > 0){// 如果匹配到了路由参数
              if(appSystemAndProgramTopMenus.includes(activeTopMenu.path)){// 如果当前访问的是应用系统、应用程序的菜单，需要特殊处理
                setShowChangeAppInfo(true)
                const appSystemName = routeParamsInfoCache?.params?.id === selectAppInfo?.appSystemId ? selectAppInfo?.appSystemName : null
                const appProgramName = routeParamsInfoCache?.params?.systemId === selectAppInfo?.appProgramId ? selectAppInfo?.appProgramName : null
                const selectAppInfoTemp = {
                  appSystemId: routeParamsInfoCache?.params?.id ?? null,
                  appSystemName,
                  appProgramId: routeParamsInfoCache?.params?.systemId ??  null,
                  appProgramName
                }
                setSelectAppInfo(selectAppInfoTemp)
                setIsLinkChange(true)
              }
              sidebarMenus = generateSidebarMenuByParams(sidebarMenus, routeParamsInfoCache.params)
            }
          }
          setActiveTopMenu(activeTopMenu)
          setSidebarMenus(sidebarMenus)
          setSelectedKeys(selectedKeys)
          setOpenKeys(openKeys)
          changeSideBarVisible(sideBarVisible)
        }
      }
      // 触发hashchange事件，防止路由生效单页面不跳转
      const event = new Event("hashchange");
      window.dispatchEvent(event);
    }
  },[currentPath, topMenus, topMenuPathMap])

  return (
    <div className="topMenu">
      <Menu className="back-end-top-menu" style={{ minWidth: 0, flex: "auto" }} onClick={handleClickHeaderMenu} selectedKeys={activeTopMenu ? [activeTopMenu.id] : []} mode="horizontal" items={formatTopMenus} />
    </div>
  );
}
