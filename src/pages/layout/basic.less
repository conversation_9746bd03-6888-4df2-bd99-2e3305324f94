html, body {
    height: 100%;
    font-size: 12px;
    font-weight: 400;
	// 滚动条
	scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
	::-webkit-scrollbar {
		width:12px;
		height:12px;
		width:8px;
		height:8px;
		background-color: transparent;
	}
	::-webkit-scrollbar:hover {
		width:12px;
		height:12px;
		width:8px;
		height:8px;
	}
	::-webkit-scrollbar-track{
		-webkit-box-shadow: none;
		background-color: none;
	}
	::-webkit-scrollbar-track:hover {
		background-color:#eee;
	}
	::-webkit-scrollbar-thumb{
		background-color: transparent;
		border-radius: 8px;
	}
	::-webkit-scrollbar-thumb:hover {
		background-color:#bababa;
	}

	.on-scrollbar{
		scrollbar-width: thin; /* Firefox */
		-ms-overflow-style: none; /* IE 10+ */
	}
	.on-scrollbar::-webkit-scrollbar-track {
		-webkit-box-shadow: none;
		background-color: transparent;
	}
	.on-scrollbar::-webkit-scrollbar-track:active {
		-webkit-box-shadow: none;
		background-color:#eee;
	}
	.on-scrollbar::-webkit-scrollbar {
		width: 8px;
		height: 8px;
		width: 8px;
		height: 8px;
		background-color: transparent;
	}
	.on-scrollbar::-webkit-scrollbar:active {
		width:8px;
		height:8px;
		width:8px;
		height:8px;
	}
	.on-scrollbar::-webkit-scrollbar-thumb {
		background-color: #bababa;
		border-radius: 8px;
	}
	.on-scrollbar::-webkit-scrollbar-thumb:hover {
		background-color:#bababa;
	}
	.on-scrollbar::-webkit-scrollbar-thumb:active {
		background-color:#bababa;
	}
}

html, body, div, span, iframe,
h1, h2, h3, h4, h5, h6, p, pre,
a, code, img,
dl, dt, dd, ol, ul, li,
form, label,
table, caption, tbody, tfoot, thead, tr, th, td, canvas,
figure, figcaption, footer, header,
menu, nav, section, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	// 当前指定的font-family 会导致devops流水线日志等使用的react-ace组件产生光标错位，输入定位错误等问题
	// 涉及到需要使用等宽字体来定义字体样式，下面的就是一些等宽字体
	// font-family: 'Courier New', Courier, 'Lucida Console', 'Andale Mono', 'Courier', 'monospace';
//    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
//   'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
//   'Noto Color Emoji';
}

ol, ul, li {
   list-style: none;
}
.layout p {
	margin: 0;
}

.com-text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.micro-tooltip-content {
	.micro-tooltip-inner {
		background: #fff;
		color: #000;
	}
	.micro-tooltip-arrow-content::before {
		background: #fff !important;
	}
}

// 所有tooltip显示的悬浮框都是白底黑字的
.ant-tooltip-content {
	.ant-tooltip-inner {
		background: #fff;
		color: #000;
	}
	.ant-tooltip-arrow-content::before {
		background: #fff !important;
	}
}

.ant-table-filter-trigger {
	color: black!important;
	transition: none!important;
	background: none!important;
}
.flex-app {
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.header-icon {
    width: 6px;
    height: 16px;
    margin-right: 12px;
    background-color: #1a9fe7;
    border-radius: 3px;
}
.micro-breadcrumb li:last-child > a {
    color: #333 !important;
}
.cursor-pointer{
	cursor: pointer;
}
// 全局隐藏文档库助手
#embedrag{
	display: none;
	.embedrag-chat-button{
		z-index: 999!important;
	}
}
//表格文字溢出
.text-overflow{
	max-width: 200px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
  }