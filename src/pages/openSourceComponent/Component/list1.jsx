import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';

import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './list.less';

import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Modal } from 'antd';

import { createBaseline,getCategories,getBaselineList,updateBaseline,delBaseline } from '@/services';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import storage from '@/utils/storage';
import { localStorageEnum } from '@/enums/storageEnum';

const { confirm } = Modal;
const { Option } = Select;

const OpenSourceComponentList = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const USERINFO = localStorageEnum.USERINFO;
  const userInfo = storage.getLocal(USERINFO);
  
  const linkList = [
    {
      title: '开源组件管理'
    }
  ]
  
  const [data, setData] = useState([]);
  const [categoriesList, setCategoriesList] = useState([]);
  const [total, setTotal] = useState(0);
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [editingRow, setEditingRow] = useState(null); // 正在编辑的行数据
  const [isAdding, setIsAdding] = useState(false); // 是否正在新增

  const onPageChange = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  // 获取分类
  const getCategoriesList = () => {
    getCategories().then((res) => {
      if(res){
        setCategoriesList(res || [])
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  // 获取列表
  const getBaselineListData = () => {
    const dataParam = {
      pageSize: pageSize,
      pageNo: currentPage,
      name: name,
      category: category
    }
    getBaselineList(dataParam).then((res) => {
      if(res){
        setData(res.records || [])
        setTotal(res.total)
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  // 创建基线
  const handleCreateBaseline = (data) => {
    let param={
      category: data.category,
      description:data.description,
      minVersion: data.minVersion,
      name:data.name,
      permit: data.permit,
      version:data.version,
    }
    createBaseline(param).then((res) => {
      if(res){
        message.success("创建成功")
        setIsAdding(false)
        setEditingRow(null)
        getBaselineListData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  // 更新基线
  const handleUpdate = (param) => {
    updateBaseline(param).then((res) => {
      if(res){
        message.success("更新成功")
        setEditingRow(null)
        getBaselineListData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  const handleChange = (value) => {
    setCurrentPage(1)
    setCategory(value)
  };

  // 新增基线
  const addBaseline = () => {
    const newRow = {
      id: 'new-' + Date.now(), // 临时ID
      name: '',
      category: '',
      version: '',
      minVersion: '',
      permit: '',
      description: '',
      isNew: true // 标记为新行
    };
    
    setData([...data, newRow]);
    setEditingRow(newRow.id);
    setIsAdding(true);
  };

  // 编辑组件
  const editComponent = (record) => {
    setEditingRow(record.id);
  };

  const showDelConfirm = (item) => {
    confirm({
      title: '删除?',
      icon: <ExclamationCircleOutlined />,
      content: `确定删除组件 ${item.name}？`,
      onOk() {
        handleDelete(item.id)
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const handleDelete = (id) => {
    delBaseline({id: id}).then((res) => {
      if(res){
        message.success("删除成功")
        getBaselineListData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  // 保存行数据
  const saveRow = (record) => {
    if (record.isNew) {
      // 新增保存
      handleCreateBaseline(record);
    } else {
      // 编辑保存
      handleUpdate(record);
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    if (isAdding) {
      // 如果是新增，移除最后一行
      setData(data.slice(0, -1));
      setIsAdding(false);
    }
    setEditingRow(null);
  };

  // 处理字段变更
  const handleFieldChange = (field, value, record) => {
    const newData = data.map(item => {
      if (item.id === record.id) {
        return { ...item, [field]: value };
      }
      return item;
    });
    setData(newData);
  };

  useEffect(() => {
    getCategoriesList();
  }, []);

  useEffect(() => {
    getBaselineListData();
  }, [currentPage, pageSize, name, category]);

  const columns = [
    {
      title: '组件名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => 
        editingRow === record.id ? (
          <Input 
            value={text} 
            onChange={(e) => handleFieldChange('name', e.target.value, record)}
          />
        ) : (
          <span>{text}</span>
        ),
    },
    {
      title: '分类',
      key: 'category',
      dataIndex: 'category',
      render: (text, record) => 
        editingRow === record.id ? (
          <Select 
            value={text} 
            style={{width: '100%'}}
            onChange={(value) => handleFieldChange('category', value, record)}
          >
            {categoriesList.map(item => (
              <Option key={`${item}`} value={item}>{`${item}`}</Option>
            ))}
          </Select>
        ) : (
          <span>{text}</span>
        ),
    },
    {
      title: '基线版本',
      dataIndex: 'version',
      key: 'version',
      render: (text, record) => 
        editingRow === record.id ? (
          <Input 
            value={text} 
            onChange={(e) => handleFieldChange('version', e.target.value, record)}
          />
        ) : (
          <span>{text}</span>
        ),
    },
    {
      title: '最低版本',
      dataIndex: 'minVersion',
      key: 'minVersion',
      render: (text, record) => 
        editingRow === record.id ? (
          <Input 
            value={text} 
            onChange={(e) => handleFieldChange('minVersion', e.target.value, record)}
          />
        ) : (
          <span>{text}</span>
        ),
    },
    {
      title: '更新人',
      dataIndex: 'manager',
      key: 'manager',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime'
    },
    {
      title: '许可证描述',
      key: 'permit',
      dataIndex: 'permit',
      render: (text, record) => 
        editingRow === record.id ? (
          <Input 
            value={text} 
            onChange={(e) => handleFieldChange('permit', e.target.value, record)}
          />
        ) : (
          <span>{text}</span>
        ),
    },
    {
      title: '描述',
      key: 'description',
      dataIndex: 'description',
      className: "text-overflow",
      render: (text, record) => 
        editingRow === record.id ? (
          <Input 
            value={text} 
            onChange={(e) => handleFieldChange('description', e.target.value, record)}
          />
        ) : (
          <span title={text}>{text}</span>
        ),
    },
    {
      title: '操作',
      key: '操作',
      render: (_, record) => (
        <Space>
          {editingRow === record.id ? (
            <>
              <Button type="link" onClick={() => saveRow(record)}>保存</Button>
              <Button type="link" onClick={cancelEdit}>取消</Button>
            </>
          ) : (
            <>
              <a onClick={() => editComponent(record)}>编辑</a>
              <a onClick={() => showDelConfirm(record)}>删除</a>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="openSourceComponent ">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info">
            <Row>
              <div className='flex-app' style={{marginBottom: '25px'}}>
                <div className='header-icon'></div>
                <div>组件基线列表</div>
              </div>
            </Row>
            <Row>
              <Col span={24} style={{display: 'flex', justifyContent: 'space-between'}}>
                {
                  userInfo.roleIds.indexOf('1914312279129419777') > -1 ? 
                  <Button style={{margin: '0px 5px'}} type='primary' onClick={addBaseline} disabled={isAdding}>
                    新增
                  </Button> : 
                  <div></div>
                }
                <div className='mySubscriptions-search'>
                  <Space>
                    <Input 
                      placeholder="名称" 
                      style={{width: '250px'}} 
                      value={name} 
                      onChange={(e) => {setName(e.target.value); setCurrentPage(1)}} 
                    />
                    <Select 
                      onChange={handleChange} 
                      style={{width: '200px'}} 
                      placeholder="分类" 
                      allowClear
                    >
                      {categoriesList.map(item => (
                        <Option key={`${item}`} value={item}>{`${item}`}</Option>
                      ))}
                    </Select>
                  </Space>
                </div>
              </Col>
            </Row>
          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div>
            <Table 
              columns={columns} 
              dataSource={data} 
              rowKey="id"
              pagination={{
                current: currentPage,
                showTotal: () => {return `共 ${total} 项数据`},
                showSizeChanger: true,
                onChange: (page, pageSize) => {onPageChange(page, pageSize)},
                total: total,
                pageSize: pageSize
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpenSourceComponentList;