
import React, { useEffect, useRef, useState } from "react";
import { Input, Tabs , Affix, Menu , Checkbox,message,Empty } from "antd";
import { history } from "umi";
import { getAllMiddlewareAppList,addFavorite,removeFavorite } from '@/services/middleware';
import "./index.less";
import "./total.less";
// import MySubscriptions from './apiManageComponent/MySubscriptions'
// import SubscripLogC from './apiManageComponent/SubscripLog'
import {
  PieChartOutlined
} from '@ant-design/icons';
import { Link, Outlet } from 'umi';





const OpenSourceManage = ({searchValue}) => {
  const [current, setCurrent] = useState('1');
  
  useEffect(() => {  
  getList()       
  }, [searchValue]);

  const getList = () => {
   
  }

  const onClick = (e) => {
  
    setCurrent(e.key);
  };
  return (
    <div className="dealPadding products-api-manage" style={{height:'100%',overflow:'auto'}}>
      <div>
        <div className="sidebar sidebar-expand">
          <div className="sidebar-content">
            <Menu mode="vertical"  selectedKeys={[current]} style={{border:'0px'}} onClick={onClick}>
              <Menu.Item key="1">
                <Link to="/openSourceComponent/list">基线管理  <PieChartOutlined /></Link>
               
              </Menu.Item>
              <Menu.Item key="2">
               
                <Link to="/openSourceComponent/use">使用情况查询  <PieChartOutlined /></Link>
              </Menu.Item>
            </Menu>
          
          </div>
        
        </div>
      </div>

      <div className="main-content">
      
        <>
        <Outlet />
        </>
       
      
       
     
        
      </div>
    </div>
  );
};


export default OpenSourceManage;
