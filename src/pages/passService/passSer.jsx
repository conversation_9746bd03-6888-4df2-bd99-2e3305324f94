import React from "react";
import { Input, Button, Space, Select, Tooltip, Affix } from "antd";
import { history } from "umi";
import "./passSer.less";
import { StarFilled, StarOutlined } from "@ant-design/icons";

const PassSer = ({ prodcut, productType, handleCollecte }) => {
  // 处理卡片点击事件，跳转到详情页面
  const handleCardClick = (api) => {
    if (api.name.indexOf("建设中") > -1) {
      return;
    }

    if (api.name === "数据脱敏服务") {
      history.push(
        `/passService/dataDesensitization/${
          api.name
        }/${1}/${api.name?.toLowerCase()}`,
        { api }
      );
    } else if (api.name === "国际化组件") {
      history.push(
        `/passService/internationalization/${
          api.name
        }/${1}/${api.name?.toLowerCase()}`,
        { api }
      );
    } else if (api.name === "节假日服务组件") {
      history.push(`/holidayDetail`, { api });
    } else if (api.name === "全文检索") {
      history.push(`/fulltextSearchDetail`, { api });
    } else if (api.name === "统一认证服务调用") {
      history.push(
        `/passService/passAuthServerDetail/${
          api.name
        }/${1}/${api.name?.toLowerCase()}`,
        { api }
      );
    } else if (api.category === "中间件") {
      history.push(
        `/passService/detail/${api.name}/${
          api.productId
        }/${api.name?.toLowerCase()}`,
        { api }
      );
    } else if (api.category === "共享组件") {
      history.push(
        `/passService/componentDetail/${api.name}/${
          api.productId ? api.productId : 1
        }`,
        { api }
      );
    } else if (api.category === "API市场") {
      history.push(`/apiMarket`);
    }

    if (api.type === "虚拟机") {
      window.open("https://cloud.trinasolar.com/vm-service/", "_blank");
    } else if (api.type === "存储") {
      window.open(
        "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=d037e4514cc44fb1869b93e7ef046124&showType=create&catalogId=1800418004157140992&testFlag=0&serviceItemId=7861fc26215a4b2ea130a493875f028b",
        "_blank"
      );
    } else if (api.type === "域名") {
      window.open(
        "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=bb8e677828d947aeacf1e3b9709b698f&showType=create&catalogId=1800417763348033536&testFlag=0&serviceItemId=4cfb4419a689489aa495d0233ae347a7",
        "_blank"
      );
    }
  };
  return (
    <div className="pass-ser" style={{ padding: "0px" }}>
      <div className="main-content">
        <div className="api-list">
          {prodcut?.length > 0
            ? prodcut.map((api) => (
                <div
                  key={api.id}
                  className={
                    api.name.indexOf("建设中") > -1
                      ? "api-card api-card-disabled"
                      : "api-card"
                  }
                  onClick={() => handleCardClick(api)}
                  style={{ cursor: "pointer" }}
                >
                  <div className="card-header">
                    <div className="card-header-main">
                      <div className="header-content">
                        <div className="api-logo">
                          <img src={`${api.logoUrl}`} alt={api.logoUrl} />
                        </div>
                        <div>
                          <h4 className="api-name">{api.name}</h4>
                          <div className="api-tags">
                            <span className="database-tag">{api.type}</span>
                            {api.version
                              ? api.version.split(",").map((item, index) =>
                                  index < 2 ? (
                                    <span
                                      className="version-tag"
                                      title={api.version}
                                    >
                                      {item}
                                    </span>
                                  ) : (
                                    ""
                                  )
                                )
                              : ""}
                          </div>
                        </div>
                      </div>
                      {api.name.indexOf("建设中") < 0 ? (
                        productType === "常用产品" ? (
                          <StarFilled
                            className="collected"
                            onClick={(e) => {
                              handleCollecte(e, "del", api.id);
                            }}
                          />
                        ) : api.isCollect ? (
                          <StarFilled
                            className="collected"
                            onClick={(e) => {
                              handleCollecte(e, "del", api.id);
                            }}
                          />
                        ) : (
                          <StarOutlined
                            className="unCollected"
                            onClick={(e) => {
                              handleCollecte(e, "add", api.id);
                            }}
                          />
                        )
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                  <div className="card-info">
                    <div className="info-item">
                      <div>{api.description}</div>
                    </div>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </div>
    </div>
  );
};

export default PassSer;
