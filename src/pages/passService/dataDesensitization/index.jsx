import React, { useEffect, useMemo, useState } from "react";
import { useParams, history } from "umi";
import { LeftOutlined } from "@ant-design/icons";
import CodeMirror from "@uiw/react-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { yaml } from "@codemirror/lang-yaml";
import BreadcrumbNav from "@/components/BreadcrumbNav/index";
import ParamsSetDrawer from "../passParamsSet";
import "./index.less";
import { CONFIG_MIDDLEWARE } from "@/services";
import { Card, Space, Spin, Table, Tabs, Typography, notification } from "antd";
import {
  dataSource,
  columns,
  detailForm,
  resCardList,
  resCardDevList,
} from "./type";
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId;

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle",
};

// 工具函数：无数据时显示 /
const displayValue = (value) => {
  return value === null || value === undefined || value === "" ? "/" : value;
};

const DataDesensitization = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { chartName, type, id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({});

  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    return () => {
      notification.destroy("apply_success_to_jump");
    };
  }, []);
  const handleBack = () => {
    history.back();
  };

  const getDetail = useMemo(() => {
    const res = detailForm.map((item) => {
      return (
        <div
          className="info-item"
          key={item.label}
          style={item.style ? item.style : {}}
        >
          <span className="label">{item.label}</span>
          <span className="value">{displayValue(item.value)}</span>
        </div>
      );
    });
    return res;
  }, [type, middlewareInfo]);

  const getContent = (item) => {
    if (item.children) {
      return item.children.map((child) => getContent(child));
    }
    if (item.title === "基本使用") {
      return (
        <>
          <p style={{ marginTop: "4px", color: "#a8a8a8" }}>{item.tip}</p>
          <Table dataSource={dataSource} columns={columns} pagination={false} />
        </>
      );
    } else {
      return (
        <>
          {item.tip && (
            <p style={{ margin: "8px 0", color: "#a8a8a8" }}>{item.tip}</p>
          )}
          <CodeMirror
            value={item.content}
            theme="light"
            extensions={[javascript(), yaml()]}
            editable={false}
            readOnly={true}
            basicSetup={{
              lineNumbers: true,
              foldGutter: true,
              highlightActiveLine: true,
              dropCursor: true,
              allowMultipleSelections: true,
              indentOnInput: true,
            }}
            style={{ overflow: "auto" }}
          />
        </>
      );
    }
  };
  const getTitles = (item) => {
    if (item.title) {
      if (item.title instanceof Array) {
        return item.title.map((title) => {
          return (
            <>
              <Typography.Text strong>{title}</Typography.Text> <br />
            </>
          );
        });
      } else {
        return <Typography.Text strong>{item.title}</Typography.Text>;
      }
    }
  };
  const getCardContent = (contentList = resCardList) => {
    return (
      <Space direction="vertical" size="middle" style={{ display: "flex" }}>
        {contentList.map((item) => {
          return (
            <Card>
              {getTitles(item)}
              {getContent(item)}
            </Card>
          );
        })}
      </Space>
    );
  };
  const linkList = [
    {
      href: "/#/productService",
      title: "产品与服务",
    },
    {
      href: `/#/passService/dataDesensitization/${type}/${id}/${chartName}`,
      title: `${type}详情`,
    },
  ];

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding nav-path-ui">
        <BreadcrumbNav list={linkList}></BreadcrumbNav>
        <div
          className="back-button"
          style={{ width: "60px" }}
          onClick={handleBack}
        >
          <LeftOutlined />
          <span>返回</span>
        </div>
      </div>
      <Spin spinning={!!loading}>
        {/* 基本信息区域 */}
        <div className="info-section dealPadding">
          <h3 className="section-title">基本信息</h3>
          <div className="info-grid">{getDetail}</div>
        </div>
      </Spin>
      {/* 基本信息区域 */}
      <div className="info-section dealPadding" style={{ marginTop: "0" }}>
        <div className="file-content">
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                label: `测试环境`,
                key: "1",
                children: (
                  <>
                    <h3 className="section-title">使用说明</h3>
                    <p style={{ color: "#a8a8a8", lineHeight: "25px" }}>
                      组件下载地址如下：
                      <a href="" target="_blank" rel="noopener noreferrer">
                        下载地址
                      </a>
                    </p>
                    <p
                      style={{
                        color: "#a8a8a8",
                        marginBottom: "8px",
                        lineHeight: "25px",
                      }}
                    >
                      详细使用说明见链接：
                      {
                        process.env.NODE_ENV === 'production'? <a
                        href="https://tasp.trinasolar.com/#/dochub/docs/detail/4020442171905896448?parentId=2&category=2&categoryTitle=产品与服务&subCategoryId=48&subCategoryTitle=共享组件&docId=4020442171905896448"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        使用说明
                      </a>:
                      <a
                      href="https://tasp-test.trinasolar.com/#/dochub/docs/detail/4020243397815693312?parentId=2&category=2&categoryTitle=%E4%BA%A7%E5%93%81%E4%B8%8E%E6%9C%8D%E5%8A%A1&subCategoryId=48&subCategoryTitle=%E5%85%B1%E4%BA%AB%E7%BB%84%E4%BB%B6"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      使用说明
                    </a>
                      }
                    </p>
                    <h3 className="section-title">springboot 使用说明如下</h3>
                    <div className="file-content">
                      {getCardContent(resCardDevList)}
                    </div>
                  </>
                ),
              },
              {
                label: `生产环境`,
                key: "2",
                children: (
                  <>
                    <h3 className="section-title">使用说明</h3>
                    <p style={{ color: "#a8a8a8", lineHeight: "25px" }}>
                      组件下载地址如下：
                      <a href="" target="_blank" rel="noopener noreferrer">
                        下载地址
                      </a>
                    </p>
                    <p
                      style={{
                        color: "#a8a8a8",
                        marginBottom: "8px",
                        lineHeight: "25px",
                      }}
                    >
                      详细使用说明见链接：
                      {
                        process.env.NODE_ENV === 'production'? <a
                        href="https://tasp.trinasolar.com/#/dochub/docs/detail/4020442171905896448?parentId=2&category=2&categoryTitle=产品与服务&subCategoryId=48&subCategoryTitle=共享组件&docId=4020442171905896448"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        使用说明
                      </a>:
                      <a
                      href="https://tasp-test.trinasolar.com/#/dochub/docs/detail/4020243397815693312?parentId=2&category=2&categoryTitle=%E4%BA%A7%E5%93%81%E4%B8%8E%E6%9C%8D%E5%8A%A1&subCategoryId=48&subCategoryTitle=%E5%85%B1%E4%BA%AB%E7%BB%84%E4%BB%B6"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      使用说明
                    </a>
                      }
                    </p>
                    <h3 className="section-title">springboot 使用说明如下</h3>
                    <div className="file-content">
                      {getCardContent(resCardList)}
                    </div>
                  </>
                ),
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default DataDesensitization;
