import React, { useEffect, useRef, useState } from "react";
import {
  Input,
  Button,
  Affix,
  Select,
  Checkbox,
  Menu,
  message,
  Empty,
} from "antd";
import "./index.less";
import "./products.less";
import { history } from "umi";
const { SubMenu, Item: MenuItem } = Menu;
import PassSer from "./passSer";
import {
  getAllMiddlewareAppList,
  addFavorite,
  removeFavorite,
} from "@/services/middleware";

const Products = ({ searchValue }) => {
  // const allData = MockAllData;
  const [curData, setCurData] = useState([]);
  const [selectedGroup, setGroup] = useState([]);
  const [showSearch, setSearch] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState(["全部"]);
  const [allList, setAllList] = useState({});
  const [productList, setProductList] = useState([]);
  const [commonproductList, setCommonproductList] = useState([]);
  const [list, setList] = useState([]);
  const [typeListData, setTypeListData] = useState([]);
  const [isEmputy, setIsEmputy] = useState([]);

  useEffect(() => {
    getList();
    //   const MockAllData =  {
    //     "favoriteProducts": [
    //         {
    //             "id": 2,
    //             "productId": 5,
    //             "name": "PostgreSQL",
    //             "description": "Postgresql by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "16.1,15.6.7,34.5.6",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/postgresql-2.4.1.svg"
    //         },
    //         {
    //             "id": 3,
    //             "productId": 1,
    //             "name": "MySQL",
    //             "description": "MySQL by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "5.7,8.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/mysql-1.12.0.svg"
    //         },
    //         {
    //             "id": 4,
    //             "productId": 4,
    //             "name": "Redis",
    //             "description": "Redis by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "6.2.6",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/redis-2.11.0.svg"
    //         },
    //         {
    //             "id": 7,
    //             "productId": 8,
    //             "name": "Rabbitmq",
    //             "description": "CloudDB RabbitMQ消息队列",
    //             "url": null,
    //             "type": "消息队列",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "3.12.7",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/rabbitmq-1.1.0.svg"
    //         },
    //         {
    //             "id": 12,
    //             "productId": null,
    //             "name": "域名申请",
    //             "description": "申请域名请从TIS提起工单，点击跳转到TIS域名申请表单",
    //             "url": "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=bb8e677828d947aeacf1e3b9709b698f&showType=create&catalogId=1800417763348033536&testFlag=0&serviceItemId=4cfb4419a689489aa495d0233ae347a7",
    //             "type": "域名",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "3.7.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/domain.png"
    //         },
    //         {
    //             "id": 38,
    //             "productId": null,
    //             "name": "报表服务调用(建设中)",
    //             "description": "支持生成和展示各类业务报表，帮助管理层进行数据分析和决策支持。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/report.png"
    //         }
    //     ],
    //     "allProducts": [
    //         {
    //             "id": 1,
    //             "productId": 38,
    //             "name": "Elasticsearch",
    //             "description": "CloudDB Elasticsearch数据库",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "6.8.22",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/elasticsearch-1.10.0.svg"
    //         },
    //         {
    //             "id": 2,
    //             "productId": 5,
    //             "name": "PostgreSQL",
    //             "description": "Postgresql by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "16.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/postgresql-2.4.1.svg"
    //         },
    //         {
    //             "id": 3,
    //             "productId": 1,
    //             "name": "MySQL",
    //             "description": "MySQL by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "5.7,8.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/mysql-1.12.0.svg"
    //         },
    //         {
    //             "id": 4,
    //             "productId": 4,
    //             "name": "Redis",
    //             "description": "Redis by Harmonycloud",
    //             "url": null,
    //             "type": "数据库",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "6.2.6",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/redis-2.11.0.svg"
    //         },
    //         {
    //             "id": 5,
    //             "productId": 33,
    //             "name": "RocketMQ",
    //             "description": "Rocketmq by Harmonycloud",
    //             "url": null,
    //             "type": "消息队列",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "4.9.2",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/rocketmq-3.1.0.svg"
    //         },
    //         {
    //             "id": 6,
    //             "productId": 32,
    //             "name": "Kafka",
    //             "description": "CloudDB Kafka消息队列",
    //             "url": null,
    //             "type": "消息队列",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "3.7.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/kafka-1.11.0.svg"
    //         },
    //         {
    //             "id": 7,
    //             "productId": 8,
    //             "name": "Rabbitmq",
    //             "description": "CloudDB RabbitMQ消息队列",
    //             "url": null,
    //             "type": "消息队列",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "3.12.7",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/rabbitmq-1.1.0.svg"
    //         },
    //         {
    //             "id": 8,
    //             "productId": 14,
    //             "name": "ZooKeeper",
    //             "description": "CloudDB Zookeeper数据库",
    //             "url": null,
    //             "type": "微服务引擎",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "3.7.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/zookeeper.png"
    //         },
    //         {
    //             "id": 10,
    //             "productId": 13,
    //             "name": "Mongodb",
    //             "description": "Cloud MongoDB一款基于分布式文件存储的数据库，旨在为 WEB 应用提供可扩展的高性能数据存储解决方案",
    //             "url": null,
    //             "type": "其他PaaS服务",
    //             "supplier": "",
    //             "category": "中间件",
    //             "version": "2.0.3",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/mongodb.svg"
    //         },
    //         {
    //             "id": 11,
    //             "productId": null,
    //             "name": "Windows虚拟机",
    //             "description": "点击卡片跳转至云管平台虚拟机申请页面",
    //             "url": "https://cloud.trinasolar.com/vm-service/",
    //             "type": "虚拟机",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "2.0.3",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/windows.png"
    //         },
    //         {
    //             "id": 12,
    //             "productId": null,
    //             "name": "域名申请",
    //             "description": "申请域名请从TIS提起工单，点击跳转到TIS域名申请表单",
    //             "url": "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=bb8e677828d947aeacf1e3b9709b698f&showType=create&catalogId=1800417763348033536&testFlag=0&serviceItemId=4cfb4419a689489aa495d0233ae347a7",
    //             "type": "域名",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "3.7.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/domain.png"
    //         },
    //         {
    //             "id": 13,
    //             "productId": null,
    //             "name": "对象存储",
    //             "description": "申请对象存储请从TIS提起工单，点击卡片跳转到TIS工单申请表单",
    //             "url": "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=d037e4514cc44fb1869b93e7ef046124&showType=create&catalogId=1800418004157140992&testFlag=0&serviceItemId=7861fc26215a4b2ea130a493875f028b",
    //             "type": "存储",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "3.7.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/objectStorage.png"
    //         },
    //         {
    //             "id": 14,
    //             "productId": null,
    //             "name": "块存储",
    //             "description": "申请块存储请从TIS提起工单，点击卡片跳转到TIS工单申请表单",
    //             "url": "https://tis.trinasolar.com/dosm/dosm/orderCreate?modelId=d037e4514cc44fb1869b93e7ef046124&showType=create&catalogId=1800418004157140992&testFlag=0&serviceItemId=7861fc26215a4b2ea130a493875f028b",
    //             "type": "存储",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "3.7.1",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/blockStorage.png"
    //         },
    //         {
    //             "id": 27,
    //             "productId": null,
    //             "name": "短信服务调用",
    //             "description": "用于发送和接收短信验证码及通知，提升用户验证与通知效率。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0,6.8.22,7.1.2",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/message.png"
    //         },
    //         {
    //             "id": 28,
    //             "productId": null,
    //             "name": "应用附件服务调用",
    //             "description": "支持应用程序中文件的上传、下载和管理，增强文件处理功能。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/file-manage.png"
    //         },
    //         {
    //             "id": 29,
    //             "productId": null,
    //             "name": "API网关服务调用(建设中)",
    //             "description": "统一管理API接口，提供高效、安全的API访问通道，促进内外部服务集成。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/api-gateway.png"
    //         },
    //         {
    //             "id": 30,
    //             "productId": null,
    //             "name": "图片服务调用(建设中)",
    //             "description": "提供图片的裁剪、放大等处理展示功能，优化图片处理体验。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/pic-service.png"
    //         },
    //         {
    //             "id": 31,
    //             "productId": null,
    //             "name": "统一认证服务调用(建设中)",
    //             "description": "集中化的用户认证机制，简化用户登录流程，提高系统安全性。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/authentication.png"
    //         },
    //         {
    //             "id": 32,
    //             "productId": null,
    //             "name": "全文检索(建设中)",
    //             "description": "快速实现数据的高效搜索，提升用户数据查询体验。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/search.png"
    //         },
    //         {
    //             "id": 33,
    //             "productId": null,
    //             "name": "微服务治理(建设中)",
    //             "description": "对分布式微服务架构进行监控、管理和协调，保障微服务的高效运行。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/micro-service.png"
    //         },
    //         {
    //             "id": 34,
    //             "productId": null,
    //             "name": "UI组件库(建设中)",
    //             "description": "提供丰富、可复用的UI组件，加速前端开发过程，保持界面风格一致。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/ui-component.png"
    //         },
    //         {
    //             "id": 35,
    //             "productId": null,
    //             "name": "国际化组件(建设中)",
    //             "description": "支持多语言、多地区的文本和内容展示，提升应用的全球适应性。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/internationalization.png"
    //         },
    //         {
    //             "id": 36,
    //             "productId": null,
    //             "name": "节假日服务组件(建设中)",
    //             "description": "提供节假日信息查询功能，方便业务逻辑中处理假期相关需求。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/holiday.png"
    //         },
    //         {
    //             "id": 37,
    //             "productId": null,
    //             "name": "操作日志服务调用(建设中)",
    //             "description": "支持统计、展示用户和系统操作日志，便于问题排查和系统审计。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/op-logs.png"
    //         },
    //         {
    //             "id": 38,
    //             "productId": null,
    //             "name": "报表服务调用(建设中)",
    //             "description": "支持生成和展示各类业务报表，帮助管理层进行数据分析和决策支持。",
    //             "url": null,
    //             "type": "共享组件",
    //             "supplier": null,
    //             "category": "共享组件",
    //             "version": "1.0.0",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/report.png"
    //         },
    //         {
    //             "id": 39,
    //             "productId": null,
    //             "name": "Linux虚拟机",
    //             "description": "点击卡片跳转至云管平台虚拟机申请页面",
    //             "url": "https://cloud.trinasolar.com/vm-service/",
    //             "type": "虚拟机",
    //             "supplier": "",
    //             "category": "外部产品",
    //             "version": "2.0.3",
    //             "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/linux.png"
    //         }
    //     ]
    // }
    //   const typeList = [];
    //   MockAllData.allProducts?.forEach((pro1)=>{
    //     MockAllData.favoriteProducts?.forEach((pro2)=>{
    //         if(pro1.id === pro2.id){
    //           pro1.isCollect = true
    //         }
    //     })
    //   })
    //   const typeListData = [{type:'常用产品',data:MockAllData.favoriteProducts}];
    //     MockAllData.allProducts?.forEach((item)=>{
    //       typeList.push(item.type)
    //     })
    //     const typeListAll = [...new Set(typeList)];
    //     typeListAll.forEach((list)=>{
    //       typeListData.push({
    //         type:list,
    //         data:[]
    //       })
    //     })
    //     typeListAll.unshift('常用产品');
    //     typeListAll.unshift('全部');
    //     setList(typeListAll)
    //     typeListData.forEach((item)=>{
    //       MockAllData.allProducts.forEach((list)=>{
    //         if(list.type === item.type){
    //           item.data.push(list)
    //         }
    //       })
    //     })
    //     searchValue ? typeListData.filter((i) => {
    //         i.data = i.data.filter((c) => c.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1)
    //       })
    //     : typeListData;
    //     let isEmputy = true;
    //       typeListData.forEach((item)=>{
    //         if(item.data.length > 0){
    //           isEmputy = false;
    //         }
    //       })
    //       setIsEmputy(isEmputy)
    //       console.log('hahah');
    //       console.log(isEmputy);
    //       console.log(typeListData)
    //     setTypeListData(typeListData)
    //     setProductList(MockAllData.allProducts || [])
    //     setCommonproductList(MockAllData.favoriteProducts || [])
  }, [searchValue]);

  const getSelectKey = (item) => {
    setSelectedKeys([item.key]);
  };
  const getList = () => {
    getAllMiddlewareAppList({}).then((res) => {
      console.log("获取到数据");
      console.log(res);
      const MockAllData = res;
      const typeList = [];
      MockAllData.allProducts?.forEach((pro1) => {
        MockAllData.favoriteProducts?.forEach((pro2) => {
          if (pro1.id === pro2.id) {
            pro1.isCollect = true;
          }
        });
      });
      const typeListData = [
        { type: "常用产品", data: MockAllData.favoriteProducts },
      ];
      MockAllData.allProducts?.forEach((item) => {
        typeList.push(item.type);
      });
      const typeListAll = [...new Set(typeList)];
      typeListAll.forEach((list) => {
        typeListData.push({
          type: list,
          data: [],
        });
      });
      typeListAll.unshift("常用产品");
      typeListAll.unshift("全部");
      setList(typeListAll);
      typeListData.forEach((item) => {
        MockAllData.allProducts.forEach((list) => {
          if (list.type === item.type) {
            item.data.push(list);
          }
        });
      });
      searchValue
        ? typeListData.filter((i) => {
            i.data = i.data.filter(
              (c) =>
                c.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1
            );
          })
        : typeListData;
      let isEmputy = true;
      typeListData.forEach((item) => {
        if (item.data.length > 0) {
          isEmputy = false;
        }
      });
      setIsEmputy(isEmputy);
      console.log("hahah");
      console.log(isEmputy);
      console.log(typeListData);
      setTypeListData(typeListData);
      setProductList(MockAllData.allProducts || []);
      setCommonproductList(MockAllData.favoriteProducts || []);
    });
  };
  const handleCollecte = (event, type, key) => {
    event.stopPropagation();
    if (type === "add") {
      addFavorite({ id: key })
        .then((res) => {
          console.log("收藏");
          console.log(res);
          if (res) {
            message.success("收藏成功");
            getList();
          } else {
            message.error("收藏失败");
          }
        })
        .catch(() => {
          message.error("收藏失败");
        });
    } else {
      removeFavorite({ id: key })
        .then((res) => {
          console.log("取消收藏");
          console.log(res);
          if (res) {
            message.success("取消收藏成功");
            getList();
          } else {
            message.error("取消收藏失败");
          }
        })
        .catch(() => {
          message.error("取消收藏失败");
        });
    }
  };
  return (
    <div
      className="products-wrapper dealPadding bgWhite"
      style={{ background: "#ffffff" }}
    >
      <div className="left-sidebar">
        <Affix offsetTop={22}>
          <div className="group-name item-padding">全部产品</div>
          <div>
            <div className="item-padding">产品类别</div>
            {list.length > 0 &&
              list.map((g) => {
                return (
                  <div className="menu-box" style={{ width: "240px" }}>
                    <Menu
                      multiple={false}
                      selectedKeys={selectedKeys}
                      onClick={(item, key) => {
                        getSelectKey(item, key);
                      }}
                    >
                      <MenuItem key={g} title={g}>
                        {g}
                      </MenuItem>
                    </Menu>
                  </div>
                );
              })}
          </div>
        </Affix>
      </div>

      <div className="main-content">
        {isEmputy ? (
          <Empty
            description="暂无数据"
            style={{
              height: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
          />
        ) : (
          typeListData.length > 0 &&
          typeListData.map((item) => {
            return (
              (selectedKeys[0] === "全部" || selectedKeys[0] === item.type) &&
              item.data.length > 0 && (
                <div className="product-box no-search-bar">
                  <div className="group-name">{item.type}</div>
                  <PassSer
                    prodcut={item.data}
                    productType={item.type}
                    handleCollecte={handleCollecte}
                  />
                </div>
              )
            );
          })
        )}
      </div>
    </div>
  );
};

const ProductService = () => {
  const [selectedFeature, setSelectedFeature] = useState(
    "提供有竞争力的高质量产品，将虚拟机、容器、存储、数据库、域名、PaaS服务、共享组件等产品技术与场景深度融合，为开发者打造安全、稳定、体验卓越的云服务。"
  );
  const [selectedTitle, setSelectedTitle] = useState("天合产品");
  const [searchValue, setSearchValue] = useState("");
  return (
    <>
      <div className="product-service">
        <div className="content-wrapper">
          {/* 页面标题 */}
          <div className="home-header dealPadding">
            <div className="header-content">
              <h1>{selectedTitle}</h1>
              <Input.Search
                placeholder="输入名称进行搜索"
                className="search"
                autoFocus
                allowClear
                style={{ width: "50%", marginBottom: "10px" }}
                onSearch={(v) => setSearchValue(v?.trim?.())}
              />
              <div className="description">{selectedFeature}</div>
            </div>
          </div>
          <Products searchValue={searchValue} />
        </div>
      </div>
    </>
  );
};

export default ProductService;
