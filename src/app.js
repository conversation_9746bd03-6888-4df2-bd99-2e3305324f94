import { actions } from "@/actions";
import { useModel, history } from "umi";
import { useEffect } from "react";
import { addPrefixToRoutes, CSRFTokenBuild } from "@/utils/tools";
import { getAllUrlParams, openWindow } from "@/utils/url";
import { getAppInfo, getAppConfig, updateHtmlTitleAndFavicon } from "@/utils/getAppInfo";
import { registerMicroApps } from "@/utils/microApps";
import { loadRagJS } from "@/utils/rag";
// import locale from "@/locale/component";
import handleErrorRedirect from "@/utils/handleErrorRedirect";
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

export function render(oldRender) {
  // 设置 dayjs 语言
  dayjs.locale('zh-cn'); 
  oldRender();
  loadRagJS();
}

// 子应用跳转信息缓存
window._MiCROAPP_JUMP_INFO_ = new Map();

// 路由跳转时子应用页面滚动重置
export function onRouteChange() {
  document.querySelector("#sub-app-box")?.scrollTo(0, 0);
}

// export const antd = (memo) => {
//   memo.configProvider = {
//     ...memo.configProvider,
//     locale: locale,
//   }

//   return memo;
// };

// 动态注册子应用, 以及路由
export const qiankun = async () => {
  const appInfo = await getAppInfo();
  const qiankunData = await registerMicroApps(appInfo?.mainAppId);
  console.log("qiankunData", qiankunData);
  return qiankunData
};

// 初始化全局数据
export async function getInitialState() {
  let appInfo = null, appConfig = null
  appInfo = await getAppInfo();
  if(appInfo?.mainAppId){
    appConfig = await getAppConfig(appInfo.mainAppId)
    updateHtmlTitleAndFavicon(appConfig)
  }
  return {
    appInfo,
    appConfig
  };
}

export function useQiankunStateForSlave() {
  const {
    jumpAndUpdateSideBarMenus
  } = useModel("menu");

  const { 
    changeSideBarVisible
  } = useModel("sysConfig");

  const { currentAppSystemAndProgramDetail } = useModel('appSystemInfo')

  const { initialState } = useModel("@@initialState");
  
  const {
    authElements,
    orgId,
    orgName,
    projectId,
    projectName,
    appId,
    clusterName,
    setAuthElements,
    setOrgId,
    setOrgName,
    setProjectId,
    setProjectName,
    setAppId,
    setClusterName,
  } = useModel("globalState");

  const changeGlobalState = (key, value) => {
    // 更改全局状态的model
    const setFnMap = {
      authElements: setAuthElements,
      orgId: setOrgId,
      orgName: setOrgName,
      projectId: setProjectId,
      projectName: setProjectName,
      appId: setAppId,
      clusterName: setClusterName,
    };
    const setFn = setFnMap[key];
    setFn && setFn(value);
  };

  useEffect(() => {
    // 因为qiankun没有提供getGlobalState, 在这里统一获取
    const globalState = {
      currentAppSystemAndProgramDetail
    };
    actions.setGlobalState(globalState);
  }, [
    currentAppSystemAndProgramDetail
  ]);

  return {
    routerInstance: history,// 主应用的路由实例
    window: window,// 主应用的window对象
    getUrlParams: getAllUrlParams,
    openWindow,
    CSRFTokenBuild,
    addPrefixToRoutes,
    onGlobalStateChange: actions.onGlobalStateChange,
    changeGlobalState: changeGlobalState,
    changeSideBarVisible,// 改变侧边栏展示隐藏
    jumpAndUpdateSideBarMenus,// 跳转并更新侧边栏所有菜单
    appInfo: initialState?.appInfo ?? null,// 当前系统的应用信息
    currentAppSystemAndProgramDetail,// 当前选中的应用系统和应用程序
    handleErrorRedirect// 错误重定向封装的方法集
  };
}
